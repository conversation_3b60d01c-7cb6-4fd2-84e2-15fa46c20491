#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>

// Configuration WiFi
const char* ssid = "VOTRE_WIFI_SSID";
const char* password = "VOTRE_WIFI_PASSWORD";

// Serveur web sur le port 80
WebServer server(80);

// Pins pour les relais/sorties
const int LIGHT_PIN = 2;
const int SHUTTER_OPEN_PIN = 4;
const int SHUTTER_CLOSE_PIN = 5;
const int HEATING_PIN = 18;
const int ALARM_PIN = 19;

// Pins pour les capteurs
const int TEMP_SENSOR_PIN = A0;
const int MOTION_SENSOR_PIN = 21;

// Variables d'état
bool lightState = false;
bool alarmState = false;
float targetTemperature = 20.0;

void setup() {
  Serial.begin(115200);
  
  // Configuration des pins
  pinMode(LIGHT_PIN, OUTPUT);
  pinMode(SHUTTER_OPEN_PIN, OUTPUT);
  pinMode(SHUTTER_CLOSE_PIN, OUTPUT);
  pinMode(HEATING_PIN, OUTPUT);
  pinMode(ALARM_PIN, OUTPUT);
  pinMode(MOTION_SENSOR_PIN, INPUT);
  
  // États initiaux
  digitalWrite(LIGHT_PIN, LOW);
  digitalWrite(SHUTTER_OPEN_PIN, LOW);
  digitalWrite(SHUTTER_CLOSE_PIN, LOW);
  digitalWrite(HEATING_PIN, LOW);
  digitalWrite(ALARM_PIN, LOW);
  
  // Connexion WiFi
  WiFi.begin(ssid, password);
  Serial.print("Connexion WiFi");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println();
  Serial.print("Connecté! IP: ");
  Serial.println(WiFi.localIP());
  
  // Configuration des routes
  server.on("/status", HTTP_GET, handleStatus);
  server.on("/command", HTTP_POST, handleCommand);
  server.on("/sensors", HTTP_GET, handleSensors);
  
  // Gestion CORS pour Flutter
  server.enableCORS(true);
  
  server.begin();
  Serial.println("Serveur démarré");
}

void loop() {
  server.handleClient();
  
  // Logique de contrôle automatique (optionnel)
  checkMotionSensor();
  controlHeating();
  
  delay(100);
}

// Route de statut
void handleStatus() {
  DynamicJsonDocument doc(200);
  doc["status"] = "online";
  doc["ip"] = WiFi.localIP().toString();
  doc["uptime"] = millis();
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

// Route principale pour les commandes
void handleCommand() {
  if (!server.hasArg("plain")) {
    server.send(400, "application/json", "{\"error\":\"No body\"}");
    return;
  }
  
  String body = server.arg("plain");
  DynamicJsonDocument doc(1024);
  deserializeJson(doc, body);
  
  String command = doc["command"];
  JsonObject parameters = doc["parameters"];
  
  DynamicJsonDocument response(512);
  response["success"] = false;
  
  // Traitement des commandes
  if (command == "light") {
    String action = parameters["action"];
    if (action == "on") {
      digitalWrite(LIGHT_PIN, HIGH);
      lightState = true;
      response["success"] = true;
      response["message"] = "Lumière allumée";
    } else if (action == "off") {
      digitalWrite(LIGHT_PIN, LOW);
      lightState = false;
      response["success"] = true;
      response["message"] = "Lumière éteinte";
    }
  }
  
  else if (command == "shutter") {
    String action = parameters["action"];
    if (action == "open") {
      digitalWrite(SHUTTER_CLOSE_PIN, LOW);
      digitalWrite(SHUTTER_OPEN_PIN, HIGH);
      delay(100); // Pulse court
      digitalWrite(SHUTTER_OPEN_PIN, LOW);
      response["success"] = true;
      response["message"] = "Volet ouvert";
    } else if (action == "close") {
      digitalWrite(SHUTTER_OPEN_PIN, LOW);
      digitalWrite(SHUTTER_CLOSE_PIN, HIGH);
      delay(100);
      digitalWrite(SHUTTER_CLOSE_PIN, LOW);
      response["success"] = true;
      response["message"] = "Volet fermé";
    }
  }
  
  else if (command == "heating") {
    float temp = parameters["temperature"];
    targetTemperature = temp;
    response["success"] = true;
    response["message"] = "Température réglée à " + String(temp) + "°C";
  }
  
  else if (command == "alarm") {
    String action = parameters["action"];
    if (action == "activate") {
      alarmState = true;
      digitalWrite(ALARM_PIN, HIGH);
      response["success"] = true;
      response["message"] = "Alarme activée";
    } else if (action == "deactivate") {
      alarmState = false;
      digitalWrite(ALARM_PIN, LOW);
      response["success"] = true;
      response["message"] = "Alarme désactivée";
    }
  }
  
  else if (command == "sensors") {
    response["success"] = true;
    response["data"]["temperature"] = readTemperature();
    response["data"]["motion"] = digitalRead(MOTION_SENSOR_PIN);
    response["data"]["light_state"] = lightState;
    response["data"]["alarm_state"] = alarmState;
  }
  
  String responseStr;
  serializeJson(response, responseStr);
  server.send(200, "application/json", responseStr);
}

// Route pour lire les capteurs
void handleSensors() {
  DynamicJsonDocument doc(512);
  doc["temperature"] = readTemperature();
  doc["motion"] = digitalRead(MOTION_SENSOR_PIN);
  doc["light_state"] = lightState;
  doc["alarm_state"] = alarmState;
  doc["target_temperature"] = targetTemperature;
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

// Lecture de température (exemple avec capteur analogique)
float readTemperature() {
  int sensorValue = analogRead(TEMP_SENSOR_PIN);
  // Conversion selon votre capteur (exemple pour LM35)
  float voltage = sensorValue * (3.3 / 4095.0);
  float temperature = voltage * 100.0;
  return temperature;
}

// Contrôle automatique du chauffage
void controlHeating() {
  float currentTemp = readTemperature();
  if (currentTemp < targetTemperature - 1.0) {
    digitalWrite(HEATING_PIN, HIGH);
  } else if (currentTemp > targetTemperature + 1.0) {
    digitalWrite(HEATING_PIN, LOW);
  }
}

// Vérification du capteur de mouvement
void checkMotionSensor() {
  if (alarmState && digitalRead(MOTION_SENSOR_PIN)) {
    // Mouvement détecté avec alarme active
    Serial.println("ALERTE: Mouvement détecté!");
    // Ici vous pourriez envoyer une notification à votre app
  }
}
