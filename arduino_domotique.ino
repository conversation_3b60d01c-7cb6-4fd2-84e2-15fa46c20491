#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <DHT.h>
#include <ESP32Servo.h>

// Configuration WiFi
const char* ssid = "VOTRE_WIFI_SSID";
const char* password = "VOTRE_WIFI_PASSWORD";

// Serveur web sur le port 80
WebServer server(80);

// === CONFIGURATION DES PINS ===
// LED
const int LED_PIN = 2;                    // LED intégrée ESP32

// DHT22 (Température et Humidité)
const int DHT_PIN = 4;                    // Pin pour DHT22
#define DHT_TYPE DHT22

// Servomoteur
const int SERVO_PIN = 18;                 // Pin PWM pour servomoteur

// Capteur magnétique à effet Hall
const int HALL_SENSOR_PIN = 21;           // Pin pour capteur Hall

// Capteur de mouvement PIR (si vous en avez un)
const int PIR_SENSOR_PIN = 19;            // Pin pour capteur PIR

// === INITIALISATION DES COMPOSANTS ===
DHT dht(DHT_PIN, DHT_TYPE);
Servo myServo;

// === VARIABLES D'ÉTAT ===
bool ledState = false;
bool alarmState = false;
bool doorOpen = false;                    // État de la porte (capteur Hall)
bool motionDetected = false;
int servoPosition = 0;                    // Position actuelle du servo (0-180°)
float currentTemperature = 0.0;
float currentHumidity = 0.0;
unsigned long lastSensorRead = 0;
const unsigned long SENSOR_INTERVAL = 2000; // Lire les capteurs toutes les 2 secondes

void setup() {
  Serial.begin(115200);
  Serial.println("🏠 Démarrage du système domotique ESP32");

  // === CONFIGURATION DES PINS ===
  pinMode(LED_PIN, OUTPUT);
  pinMode(HALL_SENSOR_PIN, INPUT_PULLUP);   // Pull-up interne pour capteur Hall
  pinMode(PIR_SENSOR_PIN, INPUT);

  // États initiaux
  digitalWrite(LED_PIN, LOW);
  ledState = false;

  // === INITIALISATION DES COMPOSANTS ===
  // Initialiser DHT22
  dht.begin();
  Serial.println("✓ DHT22 initialisé");

  // Initialiser le servomoteur
  myServo.attach(SERVO_PIN);
  myServo.write(0);  // Position initiale à 0°
  servoPosition = 0;
  Serial.println("✓ Servomoteur initialisé (position 0°)");

  // === CONNEXION WIFI ===
  WiFi.begin(ssid, password);
  Serial.print("🌐 Connexion WiFi");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println();
  Serial.print("✓ WiFi connecté! IP: ");
  Serial.println(WiFi.localIP());

  // === CONFIGURATION DU SERVEUR WEB ===
  server.on("/status", HTTP_GET, handleStatus);
  server.on("/command", HTTP_POST, handleCommand);
  server.on("/sensors", HTTP_GET, handleSensors);

  // Gestion CORS pour Flutter
  server.enableCORS(true);

  server.begin();
  Serial.println("✓ Serveur web démarré sur le port 80");
  Serial.println("🎙️ Prêt pour les commandes vocales!");

  // Première lecture des capteurs
  readAllSensors();
}

void loop() {
  server.handleClient();

  // Lire les capteurs périodiquement
  if (millis() - lastSensorRead >= SENSOR_INTERVAL) {
    readAllSensors();
    lastSensorRead = millis();
  }

  // Logique de contrôle automatique
  checkHallSensor();
  checkMotionSensor();

  delay(50);  // Délai réduit pour plus de réactivité
}

// === FONCTION DE LECTURE DE TOUS LES CAPTEURS ===
void readAllSensors() {
  // Lire DHT22 (température et humidité)
  float temp = dht.readTemperature();
  float hum = dht.readHumidity();

  if (!isnan(temp) && !isnan(hum)) {
    currentTemperature = temp;
    currentHumidity = hum;
  }

  // Lire capteur de mouvement PIR
  motionDetected = digitalRead(PIR_SENSOR_PIN);

  // Lire capteur Hall (porte/fenêtre)
  doorOpen = !digitalRead(HALL_SENSOR_PIN);  // Inversé car pull-up
}

// === ROUTES DU SERVEUR WEB ===

// Route de statut
void handleStatus() {
  DynamicJsonDocument doc(512);
  doc["status"] = "online";
  doc["device"] = "ESP32 Domotique";
  doc["ip"] = WiFi.localIP().toString();
  doc["uptime"] = millis();
  doc["wifi_strength"] = WiFi.RSSI();
  doc["free_heap"] = ESP.getFreeHeap();

  // État des composants
  doc["components"]["led"] = ledState;
  doc["components"]["servo_position"] = servoPosition;
  doc["components"]["alarm"] = alarmState;
  doc["components"]["door_open"] = doorOpen;
  doc["components"]["motion"] = motionDetected;

  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

// Route principale pour les commandes
void handleCommand() {
  if (!server.hasArg("plain")) {
    server.send(400, "application/json", "{\"error\":\"No body\"}");
    return;
  }

  String body = server.arg("plain");
  DynamicJsonDocument doc(1024);
  deserializeJson(doc, body);

  String command = doc["command"];
  JsonObject parameters = doc["parameters"];

  DynamicJsonDocument response(512);
  response["success"] = false;

  Serial.println("📨 Commande reçue: " + command);

  // === COMMANDES LED ===
  if (command == "light" || command == "led") {
    String action = parameters["action"];
    if (action == "on") {
      digitalWrite(LED_PIN, HIGH);
      ledState = true;
      response["success"] = true;
      response["message"] = "LED allumée";
      Serial.println("💡 LED allumée");
    } else if (action == "off") {
      digitalWrite(LED_PIN, LOW);
      ledState = false;
      response["success"] = true;
      response["message"] = "LED éteinte";
      Serial.println("💡 LED éteinte");
    }
  }

  // === COMMANDES SERVOMOTEUR ===
  else if (command == "servo" || command == "volet" || command == "shutter") {
    String action = parameters["action"];
    int position = parameters["position"] | -1;  // Position spécifique (0-180°)

    if (action == "open" || action == "ouvre") {
      servoPosition = 90;  // Position ouverte
      myServo.write(servoPosition);
      response["success"] = true;
      response["message"] = "Volet ouvert (90°)";
      Serial.println("🪟 Volet ouvert à 90°");
    }
    else if (action == "close" || action == "ferme") {
      servoPosition = 0;   // Position fermée
      myServo.write(servoPosition);
      response["success"] = true;
      response["message"] = "Volet fermé (0°)";
      Serial.println("🪟 Volet fermé à 0°");
    }
    else if (position >= 0 && position <= 180) {
      servoPosition = position;
      myServo.write(servoPosition);
      response["success"] = true;
      response["message"] = "Servo position: " + String(position) + "°";
      Serial.println("🔧 Servo position: " + String(position) + "°");
    }

    response["current_position"] = servoPosition;
  }

  // === COMMANDES ALARME ===
  else if (command == "alarm" || command == "alarme") {
    String action = parameters["action"];
    if (action == "activate" || action == "active") {
      alarmState = true;
      response["success"] = true;
      response["message"] = "Alarme activée";
      Serial.println("🚨 Alarme activée");

      // Clignoter la LED pour indiquer l'alarme
      for (int i = 0; i < 3; i++) {
        digitalWrite(LED_PIN, HIGH);
        delay(200);
        digitalWrite(LED_PIN, LOW);
        delay(200);
      }
      digitalWrite(LED_PIN, ledState);  // Restaurer l'état précédent

    } else if (action == "deactivate" || action == "desactive") {
      alarmState = false;
      response["success"] = true;
      response["message"] = "Alarme désactivée";
      Serial.println("🚨 Alarme désactivée");
    }
  }

  // === COMMANDE LECTURE CAPTEURS ===
  else if (command == "sensors" || command == "capteurs") {
    readAllSensors();  // Forcer une nouvelle lecture
    response["success"] = true;
    response["data"]["temperature"] = currentTemperature;
    response["data"]["humidity"] = currentHumidity;
    response["data"]["motion"] = motionDetected;
    response["data"]["door_open"] = doorOpen;
    response["data"]["led_state"] = ledState;
    response["data"]["servo_position"] = servoPosition;
    response["data"]["alarm_state"] = alarmState;
    Serial.println("📊 Lecture des capteurs demandée");
  }

  // === COMMANDE INCONNUE ===
  else {
    response["error"] = "Commande inconnue: " + command;
    response["available_commands"] = "light, servo, alarm, sensors";
    Serial.println("❌ Commande inconnue: " + command);
  }

  String responseStr;
  serializeJson(response, responseStr);
  server.send(200, "application/json", responseStr);
}

// Route pour lire les capteurs
void handleSensors() {
  readAllSensors();  // Forcer une nouvelle lecture

  DynamicJsonDocument doc(1024);

  // Données des capteurs
  doc["temperature"] = currentTemperature;
  doc["humidity"] = currentHumidity;
  doc["motion"] = motionDetected;
  doc["door_open"] = doorOpen;

  // États des actionneurs
  doc["led_state"] = ledState;
  doc["servo_position"] = servoPosition;
  doc["alarm_state"] = alarmState;

  // Informations système
  doc["uptime"] = millis();
  doc["wifi_strength"] = WiFi.RSSI();
  doc["free_heap"] = ESP.getFreeHeap();
  doc["timestamp"] = millis();

  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

// === FONCTIONS DE CONTRÔLE AUTOMATIQUE ===

// Vérification du capteur Hall (porte/fenêtre)
void checkHallSensor() {
  static bool lastDoorState = false;
  bool currentDoorState = !digitalRead(HALL_SENSOR_PIN);  // Inversé car pull-up

  if (currentDoorState != lastDoorState) {
    doorOpen = currentDoorState;
    if (doorOpen) {
      Serial.println("🚪 Porte/fenêtre ouverte (capteur Hall)");

      // Si alarme active, déclencher alerte
      if (alarmState) {
        Serial.println("🚨 ALERTE: Ouverture détectée avec alarme active!");
        // Clignoter la LED rapidement
        for (int i = 0; i < 10; i++) {
          digitalWrite(LED_PIN, HIGH);
          delay(100);
          digitalWrite(LED_PIN, LOW);
          delay(100);
        }
        digitalWrite(LED_PIN, ledState);  // Restaurer l'état
      }
    } else {
      Serial.println("🚪 Porte/fenêtre fermée");
    }
    lastDoorState = currentDoorState;
  }
}

// Vérification du capteur de mouvement PIR
void checkMotionSensor() {
  static bool lastMotionState = false;
  bool currentMotionState = digitalRead(PIR_SENSOR_PIN);

  if (currentMotionState != lastMotionState) {
    motionDetected = currentMotionState;
    if (motionDetected) {
      Serial.println("👤 Mouvement détecté (PIR)");

      // Si alarme active, déclencher alerte
      if (alarmState) {
        Serial.println("🚨 ALERTE: Mouvement détecté avec alarme active!");
        // Faire bouger le servo en alerte
        int originalPos = servoPosition;
        myServo.write(45);
        delay(500);
        myServo.write(135);
        delay(500);
        myServo.write(originalPos);
      }
    } else {
      Serial.println("👤 Fin de mouvement");
    }
    lastMotionState = currentMotionState;
  }
}
