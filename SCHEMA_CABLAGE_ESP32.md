# 🔌 Schéma de câblage ESP32 - Domotique Vocale

## 📋 Liste du matériel
- ✅ ESP32 DevKit V1
- ✅ DHT22 (Capteur température/humidité)
- ✅ Servomoteur SG90 (ou similaire)
- ✅ Capteur magnétique à effet Hall (A3144)
- ✅ LED (+ résistance 220Ω)
- ✅ Capteur PIR HC-SR501 (détection mouvement)
- ✅ Breadboard
- ✅ Fils de connexion

## 🔗 Connexions détaillées

### ESP32 Pinout utilisé:
```
ESP32 Pin    →    Composant
=========================================
GPIO2        →    LED (+ résistance 220Ω)
GPIO4        →    DHT22 (Data)
GPIO18       →    Servomoteur (Signal PWM)
GPIO19       →    Capteur PIR (OUT)
GPIO21       →    Capteur Hall (OUT)
3.3V         →    Alimentation capteurs
GND          →    Masse commune
5V           →    Servomoteur (VCC)
```

## 📐 Schéma de câblage

### 1. LED (GPIO2)
```
ESP32 GPIO2 ──[Résistance 220Ω]── LED(+) ── LED(-) ── GND
```

### 2. DHT22 (GPIO4)
```
ESP32 3.3V ── DHT22 VCC
ESP32 GPIO4 ── DHT22 DATA
ESP32 GND ── DHT22 GND
```
**Note**: Ajouter une résistance pull-up de 10kΩ entre VCC et DATA si nécessaire.

### 3. Servomoteur (GPIO18)
```
ESP32 5V ── Servo VCC (Rouge)
ESP32 GND ── Servo GND (Marron/Noir)
ESP32 GPIO18 ── Servo Signal (Orange/Jaune)
```

### 4. Capteur PIR HC-SR501 (GPIO19)
```
ESP32 5V ── PIR VCC
ESP32 GND ── PIR GND
ESP32 GPIO19 ── PIR OUT
```

### 5. Capteur Hall A3144 (GPIO21)
```
ESP32 3.3V ── Hall VCC
ESP32 GND ── Hall GND
ESP32 GPIO21 ── Hall OUT
```

## 🍞 Disposition sur breadboard

```
     ESP32 DevKit V1
    ┌─────────────────┐
    │ 3V3  ┌─┐    GND │
    │ EN   │ │    G23 │
    │ G36  │ │    G22 │
    │ G39  │U│    G1  │
    │ G34  │S│    G3  │
    │ G35  │B│    G21 │ ── Hall Sensor
    │ G32  │ │    G19 │ ── PIR Sensor
    │ G33  │C│    G18 │ ── Servo Signal
    │ G25  └─┘    G5  │
    │ G26       G17   │
    │ G27       G16   │
    │ G14       G4    │ ── DHT22 Data
    │ G12       G0    │
    │ G13       G2    │ ── LED
    │ GND       G15   │
    │ VIN       3V3   │
    └─────────────────┘

Breadboard connections:
┌─────────────────────────────────────┐
│ + Rail: ESP32 3.3V                  │
│ - Rail: ESP32 GND                   │
│                                     │
│ DHT22:     VCC(+) DATA(G4) GND(-)   │
│ PIR:       VCC(5V) OUT(G19) GND(-)  │
│ Hall:      VCC(+) OUT(G21) GND(-)   │
│ LED:       G2 ──[220Ω]── LED ── (-) │
│ Servo:     VCC(5V) SIG(G18) GND(-) │
└─────────────────────────────────────┘
```

## ⚡ Alimentation

### Tensions requises:
- **ESP32**: 3.3V (interne) / 5V (USB/VIN)
- **DHT22**: 3.3V - 5V (utiliser 3.3V)
- **Servomoteur**: 5V (important!)
- **PIR HC-SR501**: 5V
- **Capteur Hall**: 3.3V - 5V (utiliser 3.3V)
- **LED**: 3.3V avec résistance

### Consommation approximative:
- ESP32: ~240mA (WiFi actif)
- DHT22: ~1.5mA
- Servomoteur: ~100-200mA (en mouvement)
- PIR: ~65mA
- Hall: ~25mA
- LED: ~20mA

**Total**: ~450mA max → Alimentation USB suffisante

## 🔧 Configuration logicielle

### Bibliothèques Arduino nécessaires:
```cpp
#include <WiFi.h>          // WiFi ESP32 (incluse)
#include <WebServer.h>     // Serveur web (incluse)
#include <ArduinoJson.h>   // JSON (à installer)
#include <DHT.h>           // DHT sensor (à installer)
#include <ESP32Servo.h>    // Servo ESP32 (à installer)
```

### Installation des bibliothèques:
1. **ArduinoJson**: Outils → Gérer les bibliothèques → "ArduinoJson"
2. **DHT sensor library**: Chercher "DHT sensor library by Adafruit"
3. **ESP32Servo**: Chercher "ESP32Servo by Kevin Harrington"

## 🧪 Tests de fonctionnement

### 1. Test LED:
```
Commande vocale: "Allume la lumière"
Résultat: LED s'allume
```

### 2. Test Servomoteur:
```
Commande vocale: "Ouvre le volet"
Résultat: Servo tourne à 90°
```

### 3. Test DHT22:
```
API: GET /sensors
Résultat: {"temperature": 23.5, "humidity": 45.2}
```

### 4. Test PIR:
```
Action: Bouger devant le capteur
Résultat: Console série affiche "Mouvement détecté"
```

### 5. Test Hall:
```
Action: Approcher un aimant
Résultat: Console série affiche "Porte ouverte"
```

## 🚨 Sécurité et précautions

### ⚠️ Attention:
- **Servomoteur**: Utiliser 5V, pas 3.3V (risque de dysfonctionnement)
- **Alimentation**: Ne pas dépasser 5V sur VIN
- **Court-circuits**: Vérifier les connexions avant d'alimenter
- **Polarité**: Respecter + et - des composants

### 🔒 Bonnes pratiques:
- Débrancher l'alimentation avant modification
- Utiliser des résistances appropriées
- Tester composant par composant
- Vérifier avec un multimètre si doute

## 📱 Commandes vocales disponibles

### 💡 LED:
- "Allume la lumière" / "Éteins la lumière"

### 🪟 Servomoteur:
- "Ouvre le volet" / "Ferme le volet"

### 🚨 Alarme:
- "Active l'alarme" / "Désactive l'alarme"

### 📊 Capteurs:
- Les capteurs fonctionnent automatiquement
- Données disponibles via l'API /sensors

## 🐛 Dépannage

### Problèmes courants:

#### ESP32 ne se connecte pas au WiFi:
- Vérifier SSID/mot de passe
- WiFi 2.4GHz uniquement (pas 5GHz)
- Redémarrer le routeur

#### Servomoteur ne bouge pas:
- Vérifier alimentation 5V
- Vérifier connexion signal GPIO18
- Tester avec position manuelle

#### DHT22 retourne NaN:
- Vérifier connexions
- Attendre 2 secondes entre lectures
- Ajouter résistance pull-up 10kΩ

#### Capteur PIR trop sensible:
- Ajuster potentiomètres sur le module
- Attendre 1 minute après mise sous tension

#### Capteur Hall ne détecte pas:
- Utiliser aimant néodyme fort
- Distance < 1cm
- Vérifier polarité aimant

## 🎯 Prochaines étapes

1. **Téléverser le code** sur l'ESP32
2. **Noter l'IP** affichée dans le moniteur série
3. **Configurer l'app Flutter** avec cette IP
4. **Tester les commandes vocales**
5. **Ajuster les seuils** si nécessaire

Bon câblage ! 🔧⚡
