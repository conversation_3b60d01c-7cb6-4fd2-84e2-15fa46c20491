#!/bin/bash

# Script de test pour la configuration Arduino
# Ce script aide à vérifier que tout est correctement configuré

echo "🏠 Test de configuration Arduino pour la domotique vocale"
echo "======================================================="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les étapes
print_step() {
    echo -e "${BLUE}[ÉTAPE]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[ATTENTION]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERREUR]${NC} $1"
}

# 1. Vérifier les prérequis Ubuntu
print_step "Vérification des prérequis Ubuntu..."

# Vérifier si Arduino IDE est installé
if command -v arduino &> /dev/null || snap list arduino &> /dev/null; then
    print_success "Arduino IDE est installé"
else
    print_error "Arduino IDE n'est pas installé"
    echo "Installez-le avec: sudo snap install arduino"
fi

# Vérifier les permissions utilisateur
if groups $USER | grep -q "dialout"; then
    print_success "Utilisateur dans le groupe dialout"
else
    print_warning "Utilisateur pas dans le groupe dialout"
    echo "Ajoutez-vous avec: sudo usermod -a -G dialout \$USER"
    echo "Puis redémarrez votre session"
fi

# 2. Vérifier la détection USB
print_step "Vérification des ports série..."

if ls /dev/ttyUSB* /dev/ttyACM* 2>/dev/null; then
    print_success "Ports série détectés"
    echo "Ports disponibles:"
    ls -la /dev/ttyUSB* /dev/ttyACM* 2>/dev/null
else
    print_warning "Aucun port série détecté"
    echo "Connectez votre Arduino/ESP32 via USB"
fi

# 3. Vérifier la connectivité réseau
print_step "Test de connectivité réseau..."

# Demander l'IP Arduino à l'utilisateur
echo -n "Entrez l'IP de votre Arduino (ou appuyez sur Entrée pour *************): "
read arduino_ip
arduino_ip=${arduino_ip:-*************}

echo "Test de ping vers $arduino_ip..."
if ping -c 1 -W 1 $arduino_ip &> /dev/null; then
    print_success "Arduino accessible via ping"
    
    # Test du port HTTP
    echo "Test du port HTTP (80)..."
    if timeout 3 bash -c "</dev/tcp/$arduino_ip/80" 2>/dev/null; then
        print_success "Port HTTP accessible"
        
        # Test de l'API
        echo "Test de l'API Arduino..."
        response=$(curl -s --connect-timeout 3 "http://$arduino_ip/status" 2>/dev/null)
        if [ $? -eq 0 ] && [ ! -z "$response" ]; then
            print_success "API Arduino répond"
            echo "Réponse: $response"
        else
            print_warning "API Arduino ne répond pas correctement"
        fi
    else
        print_warning "Port HTTP (80) non accessible"
    fi
else
    print_error "Arduino non accessible via ping"
    echo "Vérifiez:"
    echo "- Que l'Arduino est allumé et connecté au WiFi"
    echo "- Que l'IP est correcte"
    echo "- Que vous êtes sur le même réseau"
fi

# 4. Vérifier l'environnement Flutter
print_step "Vérification de l'environnement Flutter..."

if command -v flutter &> /dev/null; then
    print_success "Flutter est installé"
    flutter --version | head -1
    
    # Vérifier les dépendances
    if [ -f "pubspec.yaml" ]; then
        print_success "Fichier pubspec.yaml trouvé"
        
        # Vérifier les dépendances importantes
        if grep -q "http:" pubspec.yaml; then
            print_success "Dépendance HTTP présente"
        else
            print_error "Dépendance HTTP manquante"
        fi
        
        if grep -q "shared_preferences:" pubspec.yaml; then
            print_success "Dépendance shared_preferences présente"
        else
            print_error "Dépendance shared_preferences manquante"
        fi
    else
        print_error "Fichier pubspec.yaml non trouvé"
        echo "Exécutez ce script depuis le répertoire racine du projet Flutter"
    fi
else
    print_error "Flutter n'est pas installé"
fi

# 5. Test des commandes vocales (simulation)
print_step "Test des commandes vocales (simulation)..."

echo "Commandes vocales supportées:"
echo "💡 Lumières: 'Allume la lumière' / 'Éteins la lumière'"
echo "🪟 Volets: 'Ouvre le volet' / 'Ferme le volet'"
echo "🌡️ Chauffage: 'Monte le chauffage' / 'Baisse le chauffage'"
echo "🚨 Alarme: 'Active l'alarme' / 'Désactive l'alarme'"

# 6. Résumé et recommandations
echo ""
echo "======================================================="
print_step "Résumé et prochaines étapes"

echo ""
echo "📋 Pour terminer la configuration:"
echo "1. Téléversez le code Arduino sur votre ESP32/Arduino"
echo "2. Notez l'IP affichée dans le moniteur série"
echo "3. Lancez l'application Flutter"
echo "4. Allez dans Configuration Arduino (icône circuit)"
echo "5. Entrez l'IP de votre Arduino et testez la connexion"
echo "6. Testez les commandes vocales"

echo ""
echo "🔧 En cas de problème:"
echo "- Vérifiez les connexions physiques"
echo "- Consultez le moniteur série Arduino"
echo "- Vérifiez que Arduino et téléphone sont sur le même WiFi"
echo "- Redémarrez l'Arduino si nécessaire"

echo ""
echo "📚 Documentation complète dans: ARDUINO_SETUP_GUIDE.md"

echo ""
print_success "Test de configuration terminé!"
