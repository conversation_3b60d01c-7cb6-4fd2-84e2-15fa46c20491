import 'package:flutter_tts/flutter_tts.dart';
import 'package:flutter/foundation.dart';
import 'package:voice_assistant/services/voice_detection_service.dart';
import 'package:flutter/material.dart';
import 'package:audio_session/audio_session.dart';
import 'dart:io';

/// États possibles du service TTS
enum TtsState { playing, stopped, paused, continued }

/// Service de synthèse vocale
/// Gère la conversion texte-parole avec gestion des erreurs et optimisation audio
class TtsService {
  static TtsService? _instance;

  static TtsService get instance {
    _instance ??= TtsService._internal();
    return _instance!;
  }

  final FlutterTts _flutterTts;
  final VoiceDetectionService _voiceDetectionService;

  TtsState _ttsState = TtsState.stopped;
  bool _isInitialized = false;
  bool _isCoordinatingWithVoiceService = true;

  /// Constructeur interne avec injection de dépendances optionnelle
  TtsService._internal({
    FlutterTts? flutterTts,
    VoiceDetectionService? voiceDetectionService,
  }) : _flutterTts = flutterTts ?? FlutterTts(),
       _voiceDetectionService =
           voiceDetectionService ?? VoiceDetectionService();

  /// Méthode factory par défaut qui utilise l'instance singleton
  factory TtsService() => instance;

  /// Méthode pour réinitialiser l'instance (utile pour les tests)
  @visibleForTesting
  static void resetInstance() {
    _instance = null;
  }

  /// Méthode pour injecter des mocks (utile pour les tests)
  @visibleForTesting
  static void setMocks({
    FlutterTts? flutterTts,
    VoiceDetectionService? voiceDetectionService,
  }) {
    _instance = TtsService._internal(
      flutterTts: flutterTts,
      voiceDetectionService: voiceDetectionService,
    );
  }

  /// Configure si le service doit coordonner avec le service de reconnaissance vocale
  set coordinateWithVoiceService(bool value) {
    _isCoordinatingWithVoiceService = value;
  }

  /// Initialise le service TTS avec la configuration optimale
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Configuration de la session audio
      final session = await AudioSession.instance;
      await _configureAudioSession(session);

      // Configuration du moteur TTS
      await _configureTtsEngine();

      // Configuration des écouteurs d'événements
      _setupEventListeners();

      _isInitialized = true;
      debugPrint('Service TTS initialisé avec succès');
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du TTS: $e');
      rethrow;
    }
  }

  /// Configure la session audio pour une qualité optimale
  Future<void> _configureAudioSession(AudioSession session) async {
    await session.configure(
      AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playback,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
        avAudioSessionMode: AVAudioSessionMode.spokenAudio,
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.audibilityEnforced,
          usage: AndroidAudioUsage.assistanceAccessibility,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: false,
      ),
    );
  }

  /// Configure le moteur TTS avec les paramètres optimaux
  Future<void> _configureTtsEngine() async {
    await _flutterTts.setLanguage('fr-FR');
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);

    if (Platform.isAndroid) {
      await _flutterTts.setQueueMode(1);
      await _flutterTts.awaitSpeakCompletion(true);
    }

    if (Platform.isIOS) {
      await _flutterTts.setSharedInstance(true);
      await _flutterTts
          .setIosAudioCategory(IosTextToSpeechAudioCategory.playback, [
            IosTextToSpeechAudioCategoryOptions.allowBluetooth,
            IosTextToSpeechAudioCategoryOptions.allowBluetoothA2DP,
            IosTextToSpeechAudioCategoryOptions.mixWithOthers,
            IosTextToSpeechAudioCategoryOptions.defaultToSpeaker,
          ]);
    }
  }

  /// Configure les écouteurs d'événements pour le TTS
  void _setupEventListeners() {
    _flutterTts.setStartHandler(() {
      _ttsState = TtsState.playing;
    });

    _flutterTts.setCompletionHandler(() {
      _ttsState = TtsState.stopped;
      _restoreVoiceDetection();
    });

    _flutterTts.setCancelHandler(() {
      _ttsState = TtsState.stopped;
      _restoreVoiceDetection();
    });

    _flutterTts.setErrorHandler((msg) {
      _ttsState = TtsState.stopped;
      debugPrint("Erreur TTS: $msg");
      _restoreVoiceDetection();
    });
  }

  // Sauvegarde l'état de l'écoute et la désactive si nécessaire
  bool _pauseVoiceDetectionIfNeeded() {
    if (!_isCoordinatingWithVoiceService) return false;

    bool wasListening = _voiceDetectionService.continuousListening;
    if (wasListening) {
      _voiceDetectionService.stopListening();
    }
    return wasListening;
  }

  // Restaure l'état de l'écoute vocale après dictée
  Future<void> _restoreVoiceDetection() async {
    if (!_isCoordinatingWithVoiceService) return;

    // Ne pas réactiver automatiquement l'écoute vocale
    // La réactivation se fera uniquement via le bouton d'écoute
    debugPrint('TTS terminé, l\'écoute vocale reste désactivée');

    // Assurez-vous que le service de reconnaissance vocale est bien arrêté
    _voiceDetectionService.stopListening();
  }

  // Méthode pour dire un texte à haute voix
  Future<void> speak(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_ttsState == TtsState.playing) {
      await stop();
    }

    // Désactiver temporairement l'écoute pendant la dictée
    _pauseVoiceDetectionIfNeeded();

    try {
      // Activer la session audio avant de parler
      final session = await AudioSession.instance;
      await session.setActive(true);

      // Utiliser un volume élevé pour les annonces
      await _flutterTts.setVolume(1.0);

      // Parler avec une priorité élevée
      await _flutterTts.speak(text);

      // Utiliser un délai pour permettre l'exécution du callbackHandler
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      print('Erreur lors de l\'annonce vocale: $e');
    }
  }

  // Arrêter la lecture en cours
  Future<void> stop() async {
    await _flutterTts.stop();
    _ttsState = TtsState.stopped;
  }

  // Mettre en pause la lecture
  Future<void> pause() async {
    await _flutterTts.pause();
    _ttsState = TtsState.paused;
  }

  // Annoncer un rappel à voix haute
  Future<void> announceReminder(String title, {String? body}) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Activer la session audio avant de parler
      final session = await AudioSession.instance;
      await session.setActive(true);

      String textToAnnounce = "Rappel: $title";
      if (body != null && body.isNotEmpty) {
        textToAnnounce += ". $body";
      }

      // Utiliser un volume plus élevé pour les rappels
      await _flutterTts.setVolume(1.0);

      // Annoncer le rappel
      await _flutterTts.speak(textToAnnounce);

      // Utiliser un délai pour permettre l'exécution du callbackHandler
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      print('Erreur lors de l\'annonce du rappel: $e');
    }
  }

  // Méthode pour annoncer les résultats de recherche
  Future<void> announceSearchResults(List<dynamic> results) async {
    if (results.isEmpty) {
      await speak("Je n'ai trouvé aucun résultat pour cette recherche.");
      return;
    }

    await speak("Voici les résultats de votre recherche:");

    // Pause entre l'introduction et les résultats
    await Future.delayed(const Duration(milliseconds: 500));

    // Limiter à 3 résultats pour ne pas surcharger l'utilisateur
    final int maxResults = results.length > 3 ? 3 : results.length;

    for (int i = 0; i < maxResults; i++) {
      final result = results[i];
      String text;

      if (result is Map<String, dynamic>) {
        text = result['title'] ?? 'Résultat ${i + 1}';
        if (result['description'] != null && result['description'].isNotEmpty) {
          text += ': ${result['description']}';
        }
      } else {
        text = result.toString();
      }

      await speak(text);

      // Pause entre les résultats
      if (i < maxResults - 1) {
        await Future.delayed(const Duration(milliseconds: 800));
      }
    }
  }

  // Libérer les ressources
  Future<void> dispose() async {
    await _flutterTts.stop();
  }
}
