import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';

class ThemeService extends ChangeNotifier {
  static const String _themeModeKey = 'theme_mode';
  static const String _primaryColorKey = 'primary_color';
  static const String _fontFamilyKey = 'font_family';

  late SharedPreferences _prefs;
  ThemeMode _themeMode = ThemeMode.system;
  Color _primaryColor = const Color(0xFF6C63FF);
  String _fontFamily = 'Poppins';

  ThemeMode get themeMode => _themeMode;
  Color get primaryColor => _primaryColor;
  String get fontFamily => _fontFamily;

  ThemeService() {
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    _prefs = await SharedPreferences.getInstance();
    _themeMode = ThemeMode.values[_prefs.getInt(_themeModeKey) ?? 0];
    _primaryColor = Color(_prefs.getInt(_primaryColorKey) ?? 0xFF6C63FF);
    _fontFamily = _prefs.getString(_fontFamilyKey) ?? 'Poppins';
    notifyListeners();
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    await _prefs.setInt(_themeModeKey, mode.index);
    notifyListeners();
  }

  Future<void> setPrimaryColor(Color color) async {
    _primaryColor = color;
    await _prefs.setInt(_primaryColorKey, color.value);
    notifyListeners();
  }

  Future<void> setFontFamily(String family) async {
    _fontFamily = family;
    await _prefs.setString(_fontFamilyKey, family);
    notifyListeners();
  }

  ThemeData getThemeData(BuildContext context) {
    final isDark =
        _themeMode == ThemeMode.dark ||
        (_themeMode == ThemeMode.system &&
            MediaQuery.of(context).platformBrightness == Brightness.dark);

    final colorScheme =
        isDark
            ? ColorScheme.dark(
              primary: _primaryColor,
              secondary: const Color(0xFF03DAC6),
              surface: const Color(0xFF1E1E1E),
            )
            : ColorScheme.light(
              primary: _primaryColor,
              secondary: const Color(0xFF03DAC6),
              surface: const Color(0xFFF8F9FA),
              onSurface: const Color(0xFF212121),
              background: const Color(0xFFFFFFFF),
              onBackground: const Color(0xFF212121),
            );

    return ThemeData(
      useMaterial3: true,
      brightness: isDark ? Brightness.dark : Brightness.light,
      colorScheme: colorScheme,
      primaryColor: _primaryColor,
      scaffoldBackgroundColor: colorScheme.surface,
      textTheme:
          isDark
              ? Typography.material2021().white.apply(
                fontFamily: GoogleFonts.getFont(_fontFamily).fontFamily,
              )
              : Typography.material2021().black.apply(
                fontFamily: GoogleFonts.getFont(_fontFamily).fontFamily,
              ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: _primaryColor),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: isDark ? const Color(0xFF2C2C2C) : const Color(0xFFEEF2F6),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 16,
          horizontal: 20,
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        centerTitle: true,
      ),
      cardTheme: CardTheme(
        color: colorScheme.surface,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }
}
