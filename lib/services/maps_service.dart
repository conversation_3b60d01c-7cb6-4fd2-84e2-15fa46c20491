import 'package:android_intent_plus/android_intent.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:location/location.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:math';
import 'package:voice_assistant/services/tts_service.dart';

class MapsService {
  static final MapsService _instance = MapsService._internal();
  factory MapsService() => _instance;

  MapsService._internal();

  // Contrôleur pour la carte FlutterMap (OpenStreetMap)
  MapController? _mapController;
  final Location _location = Location();
  final TtsService _ttsService = TtsService();

  // État de la navigation
  bool _isNavigating = false;
  List<LatLng> _routePoints = [];
  List<_NavigationInstruction> _navigationInstructions = [];
  int _currentInstructionIndex = 0;
  Timer? _locationUpdateTimer;
  StreamSubscription<LocationData>? _locationSubscription;

  // Getters pour l'état de navigation
  bool get isNavigating => _isNavigating;
  List<LatLng> get routePoints => _routePoints;

  // Polyline pour l'affichage de l'itinéraire
  Polyline? _routePolyline;
  Polyline? get routePolyline => _routePolyline;

  // Callback pour notifier les changements d'état
  VoidCallback? _onStateChanged;

  // Getter pour accéder au contrôleur de carte
  MapController? get mapController => _mapController;

  // Définir le contrôleur de carte
  void setMapController(MapController controller) {
    _mapController = controller;
  }

  // Définir le callback pour les changements d'état
  void setStateChangeCallback(VoidCallback callback) {
    _onStateChanged = callback;
  }

  // Notifier les changements d'état
  void _notifyStateChanged() {
    _onStateChanged?.call();
  }

  // Obtenir la position actuelle
  Future<LatLng?> getCurrentPosition() async {
    try {
      // Vérifier les permissions de localisation
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          return null;
        }
      }

      PermissionStatus permissionStatus = await _location.hasPermission();
      if (permissionStatus == PermissionStatus.denied) {
        permissionStatus = await _location.requestPermission();
        if (permissionStatus != PermissionStatus.granted) {
          return null;
        }
      }

      // Obtenir la position actuelle
      final locationData = await _location.getLocation();
      return LatLng(locationData.latitude!, locationData.longitude!);
    } catch (e) {
      debugPrint('Erreur lors de la récupération de la position: $e');
      return null;
    }
  }

  // Vérifier si la navigation est disponible
  bool isNavigationAvailable() {
    return true; // Navigation disponible sur toutes les plateformes maintenant
  }

  // Lancer la navigation externe
  Future<bool> navigateTo(String destination) async {
    if (Platform.isAndroid) {
      try {
        // Utiliser OSM And pour la navigation externe (si disponible)
        final intent = AndroidIntent(
          action: 'action_view',
          data: Uri.encodeFull('geo:0,0?q=$destination'),
        );

        await intent.launch();
        return true;
      } catch (e) {
        debugPrint('Erreur lors du lancement de la navigation externe: $e');
        // Continuer avec la navigation interne si externe échoue
      }
    }

    // Obtenir les coordonnées de la destination
    final destinationCoords = await getCoordinatesFromAddress(destination);
    if (destinationCoords == null) {
      _ttsService.speak(
        "Je n'ai pas pu trouver cette destination. Veuillez essayer avec une adresse plus précise.",
      );
      return false;
    }

    // Obtenir la position actuelle
    final currentPosition = await getCurrentPosition();
    if (currentPosition == null) {
      _ttsService.speak(
        "Je n'ai pas pu déterminer votre position actuelle. Vérifiez que la localisation est activée.",
      );
      return false;
    }

    // Lancer la navigation interne
    return await startVoiceNavigation(
      currentPosition,
      destinationCoords,
      destination,
    );
  }

  // Démarrer la navigation vocale interne
  Future<bool> startVoiceNavigation(
    LatLng startPosition,
    LatLng destinationPosition,
    String destinationName,
  ) async {
    if (_isNavigating) {
      await stopNavigation();
    }

    _ttsService.speak("Calcul de l'itinéraire vers $destinationName");

    try {
      // Calculer l'itinéraire avec OSRM
      final routeData = await _calculateRoute(
        startPosition,
        destinationPosition,
      );
      if (routeData == null) {
        _ttsService.speak(
          "Je n'ai pas pu calculer d'itinéraire vers cette destination.",
        );
        return false;
      }

      // Extraire les points de l'itinéraire
      _routePoints = routeData.points;
      debugPrint('Itinéraire calculé avec ${_routePoints.length} points');

      // Créer la polyline pour l'affichage
      _routePolyline = Polyline(
        points: _routePoints,
        color: Colors.blue,
        strokeWidth: 4.0,
      );
      debugPrint('Polyline créée pour l\'affichage');

      // Extraire les instructions de navigation
      _navigationInstructions = routeData.instructions;
      _currentInstructionIndex = 0;
      debugPrint('${_navigationInstructions.length} instructions de navigation préparées');

      // Marquer comme en navigation
      _isNavigating = true;

      // Notifier les changements d'état
      _notifyStateChanged();

      // Démarrer le suivi de position
      _startLocationTracking();

      // Annoncer le début de navigation
      _ttsService.speak(
        "Navigation démarrée. ${_formatDistance(routeData.distance)} jusqu'à destination.",
      );

      // Annoncer la première instruction après un délai
      if (_navigationInstructions.isNotEmpty) {
        Future.delayed(const Duration(seconds: 2), () {
          if (_isNavigating && _navigationInstructions.isNotEmpty) {
            _announceInstruction(_navigationInstructions[0]);
          }
        });
      }

      return true;
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la navigation: $e');
      _ttsService.speak(
        "Une erreur est survenue lors du démarrage de la navigation.",
      );
      return false;
    }
  }

  // Arrêter la navigation
  Future<void> stopNavigation() async {
    if (!_isNavigating) return;

    debugPrint('Arrêt de la navigation');

    // Arrêter le suivi de position
    _locationUpdateTimer?.cancel();
    await _locationSubscription?.cancel();
    _locationSubscription = null;

    // Réinitialiser les données de navigation
    _routePoints = [];
    _navigationInstructions = [];
    _currentInstructionIndex = 0;
    _routePolyline = null;
    _isNavigating = false;

    // Notifier les changements d'état
    _notifyStateChanged();

    // Annoncer la fin de navigation
    _ttsService.speak("Navigation terminée.");
  }

  // Démarrer le suivi de position
  void _startLocationTracking() {
    // Arrêter tout suivi existant
    _locationUpdateTimer?.cancel();
    _locationSubscription?.cancel();

    // Configurer la localisation pour des mises à jour fréquentes
    _location.changeSettings(
      accuracy: LocationAccuracy.high,
      interval: 1000, // 1 seconde
      distanceFilter: 5, // 5 mètres
    );

    // S'abonner aux mises à jour de localisation
    _locationSubscription = _location.onLocationChanged.listen((locationData) {
      if (!_isNavigating) return;

      final currentPosition = LatLng(
        locationData.latitude!,
        locationData.longitude!,
      );

      // Mettre à jour la carte avec la nouvelle position
      if (_mapController != null) {
        _mapController!.move(currentPosition, _mapController!.camera.zoom);
      }

      // Vérifier la distance par rapport aux instructions
      _checkNavigationProgress(currentPosition);
    });
  }

  // Vérifier le progrès de la navigation et annoncer les instructions
  void _checkNavigationProgress(LatLng currentPosition) {
    if (_navigationInstructions.isEmpty ||
        _currentInstructionIndex >= _navigationInstructions.length) {
      return;
    }

    // Instruction actuelle
    final currentInstruction =
        _navigationInstructions[_currentInstructionIndex];

    // Calculer la distance entre la position actuelle et le point de manœuvre
    final distanceToManeuver = _calculateDistance(
      currentPosition,
      currentInstruction.location,
    );

    // Si l'utilisateur est proche du point de manœuvre
    if (distanceToManeuver <= 30) {
      // 30 mètres
      // S'il reste des instructions, passer à la suivante
      if (_currentInstructionIndex < _navigationInstructions.length - 1) {
        _currentInstructionIndex++;
        _announceInstruction(_navigationInstructions[_currentInstructionIndex]);
      }
      // Sinon, si c'est la dernière instruction, vérifier si on est arrivé
      else if (_currentInstructionIndex == _navigationInstructions.length - 1) {
        // Si on est proche de la destination finale
        if (distanceToManeuver < 20) {
          // 20 mètres
          _ttsService.speak("Vous êtes arrivé à destination.");
          stopNavigation();
        }
      }
    }
    // Si on approche du point de manœuvre, annoncer l'approche
    else if (distanceToManeuver <= 100 && !currentInstruction.announced) {
      // 100 mètres
      _announceApproaching(currentInstruction, distanceToManeuver);
      // Marquer comme annoncée pour éviter les répétitions
      _navigationInstructions[_currentInstructionIndex].announced = true;
    }

    // Vérifier si l'utilisateur s'est éloigné de l'itinéraire
    _checkIfOffRoute(currentPosition);
  }

  // Vérifier si l'utilisateur s'est éloigné de l'itinéraire
  void _checkIfOffRoute(LatLng currentPosition) {
    if (_routePoints.isEmpty) return;

    // Calculer la distance minimale à l'itinéraire
    double minDistance = double.infinity;
    for (var point in _routePoints) {
      final distance = _calculateDistance(currentPosition, point);
      if (distance < minDistance) {
        minDistance = distance;
      }
    }

    // Si la distance est trop grande, considérer comme hors itinéraire
    if (minDistance > 50) {
      // 50 mètres
      _ttsService.speak(
        "Vous vous êtes éloigné de l'itinéraire. Recalcul en cours.",
      );

      // Obtenir la destination (dernier point de l'itinéraire)
      final destination = _routePoints.last;

      // Recalculer l'itinéraire
      startVoiceNavigation(currentPosition, destination, "destination");
    }
  }

  // Annoncer une instruction de navigation
  void _announceInstruction(_NavigationInstruction instruction) {
    _ttsService.speak(instruction.text);
  }

  // Annoncer l'approche d'une manœuvre
  void _announceApproaching(
    _NavigationInstruction instruction,
    double distance,
  ) {
    final distanceText = _formatDistance(distance);
    _ttsService.speak("Dans $distanceText, ${instruction.text}");
  }

  // Déplacer la caméra vers une position spécifiée sur la carte
  Future<bool> moveCamera(LatLng position, {double zoom = 15.0}) async {
    if (_mapController == null) {
      debugPrint('Contrôleur de carte non initialisé');
      return false;
    }

    try {
      _mapController!.move(position, zoom);
      return true;
    } catch (e) {
      debugPrint('Erreur lors du déplacement de la caméra: $e');
      return false;
    }
  }

  // Créer un marqueur sur la carte
  Marker createMarker({
    required LatLng position,
    required String id,
    String title = '',
    String snippet = '',
    Widget? icon,
  }) {
    return Marker(
      point: position,
      width: 80,
      height: 80,
      child: icon ?? const Icon(Icons.location_on, color: Colors.red, size: 40),
    );
  }

  // Calculer un itinéraire entre deux points avec OSRM
  Future<_RouteData?> _calculateRoute(LatLng start, LatLng destination) async {
    try {
      debugPrint('Calcul d\'itinéraire de ${start.latitude},${start.longitude} vers ${destination.latitude},${destination.longitude}');

      // Former l'URL OSRM pour le calcul d'itinéraire
      final url =
          'https://router.project-osrm.org/route/v1/driving/'
          '${start.longitude},${start.latitude};'
          '${destination.longitude},${destination.latitude}'
          '?steps=true&geometries=geojson&overview=full&annotations=true';

      debugPrint('URL OSRM: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'VoiceAssistantApp/1.0',
        },
      ).timeout(const Duration(seconds: 10));

      debugPrint('Réponse OSRM: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('Données OSRM reçues: ${data['code']}');

        // Vérifier si un itinéraire a été trouvé
        if (data['code'] != 'Ok') {
          debugPrint('Erreur OSRM: ${data['code']} - ${data['message'] ?? 'Aucun message'}');
          return null;
        }

        if (data['routes'] == null || data['routes'].isEmpty) {
          debugPrint('Aucun itinéraire trouvé dans la réponse');
          return null;
        }

        final route = data['routes'][0];
        debugPrint('Route trouvée avec distance: ${route['distance']}m');

        // Extraire la distance totale
        final distance = (route['distance'] as num).toDouble();

        // Extraire les points de l'itinéraire
        final geometry = route['geometry'];
        if (geometry == null || geometry['coordinates'] == null) {
          debugPrint('Géométrie manquante dans la réponse');
          return null;
        }

        final coordinates = geometry['coordinates'] as List;
        debugPrint('Nombre de points de coordonnées: ${coordinates.length}');

        List<LatLng> routePoints = [];
        try {
          routePoints = coordinates.map((point) {
            if (point is List && point.length >= 2) {
              return LatLng(
                (point[1] as num).toDouble(),
                (point[0] as num).toDouble()
              );
            } else {
              throw Exception('Format de coordonnée invalide: $point');
            }
          }).toList();
        } catch (e) {
          debugPrint('Erreur lors de l\'extraction des coordonnées: $e');
          return null;
        }

        debugPrint('Points d\'itinéraire extraits: ${routePoints.length}');

        // Extraire les instructions de navigation
        List<_NavigationInstruction> instructions = [];
        final legs = route['legs'];

        if (legs != null && legs.isNotEmpty) {
          for (var leg in legs) {
            final steps = leg['steps'];
            if (steps != null) {
              for (var step in steps) {
                try {
                  final maneuver = step['maneuver'];
                  if (maneuver != null && maneuver['location'] != null) {
                    final location = LatLng(
                      (maneuver['location'][1] as num).toDouble(),
                      (maneuver['location'][0] as num).toDouble(),
                    );

                    // Former le texte de l'instruction
                    String instructionText = _formatInstruction(
                      maneuver['type'] ?? 'continue',
                      maneuver['modifier'],
                      step['name'] ?? '',
                    );

                    instructions.add(
                      _NavigationInstruction(
                        text: instructionText,
                        location: location,
                        distance: (step['distance'] as num?)?.toDouble() ?? 0.0,
                      ),
                    );
                  }
                } catch (e) {
                  debugPrint('Erreur lors de l\'extraction d\'une instruction: $e');
                  // Continuer avec les autres instructions
                }
              }
            }
          }
        }

        debugPrint('Instructions extraites: ${instructions.length}');

        return _RouteData(
          points: routePoints,
          instructions: instructions,
          distance: distance,
        );
      } else {
        debugPrint(
          'Erreur lors du calcul d\'itinéraire: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } on TimeoutException {
      debugPrint('Timeout lors du calcul d\'itinéraire');
      return null;
    } catch (e) {
      debugPrint('Exception lors du calcul d\'itinéraire: $e');
      return null;
    }
  }

  // Formater une instruction de navigation
  String _formatInstruction(String type, String? modifier, String name) {
    String instruction = '';

    switch (type) {
      case 'turn':
        if (modifier == 'left') {
          instruction = 'Tournez à gauche';
        } else if (modifier == 'right') {
          instruction = 'Tournez à droite';
        } else if (modifier == 'slight left') {
          instruction = 'Tournez légèrement à gauche';
        } else if (modifier == 'slight right') {
          instruction = 'Tournez légèrement à droite';
        } else if (modifier == 'sharp left') {
          instruction = 'Tournez fortement à gauche';
        } else if (modifier == 'sharp right') {
          instruction = 'Tournez fortement à droite';
        } else {
          instruction = 'Tournez';
        }
        break;
      case 'new name':
      case 'continue':
        instruction = 'Continuez tout droit';
        break;
      case 'merge':
        instruction = 'Insérez-vous';
        break;
      case 'ramp':
        instruction = 'Prenez la bretelle';
        break;
      case 'roundabout':
        instruction = 'Entrez dans le rond-point';
        break;
      case 'rotary':
        instruction = 'Entrez dans le carrefour giratoire';
        break;
      case 'arrive':
        instruction = 'Vous êtes arrivé à destination';
        break;
      default:
        instruction = 'Continuez';
    }

    // Ajouter le nom de la rue si disponible
    if (name.isNotEmpty) {
      instruction += ' sur $name';
    }

    return instruction;
  }

  // Formater une distance pour l'annonce vocale
  String _formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      // Arrondir aux 10 mètres près
      final roundedDistance = (distanceInMeters / 10).round() * 10;
      return '$roundedDistance mètres';
    } else {
      // Convertir en kilomètres avec une décimale
      final distanceInKm = (distanceInMeters / 100).round() / 10;
      return '$distanceInKm kilomètres';
    }
  }

  // Calculer la distance entre deux points (formule de Haversine)
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // en mètres

    final lat1 = point1.latitude * pi / 180;
    final lat2 = point2.latitude * pi / 180;
    final dLat = (point2.latitude - point1.latitude) * pi / 180;
    final dLon = (point2.longitude - point1.longitude) * pi / 180;

    final a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  // Helper pour analyser une adresse en coordonnées (geocoding)
  Future<LatLng?> getCoordinatesFromAddress(String address) async {
    try {
      debugPrint('Géocodage de l\'adresse: $address');

      // Encoder l'adresse pour l'URL
      final encodedAddress = Uri.encodeComponent(address);

      // Requête à l'API Nominatim
      final url =
          'https://nominatim.openstreetmap.org/search'
          '?q=$encodedAddress&format=json&limit=1&accept-language=fr';

      debugPrint('URL Nominatim: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {'User-Agent': 'VoiceAssistantApp/1.0'},
      ).timeout(const Duration(seconds: 10));

      debugPrint('Réponse Nominatim: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List data = json.decode(response.body);
        debugPrint('Résultats trouvés: ${data.length}');

        if (data.isEmpty) {
          debugPrint('Aucun résultat pour cette adresse: $address');
          return null;
        }

        final result = data[0];
        final double lat = double.parse(result['lat']);
        final double lon = double.parse(result['lon']);

        debugPrint('Coordonnées trouvées: $lat, $lon pour ${result['display_name']}');

        return LatLng(lat, lon);
      } else {
        debugPrint('Erreur lors du géocodage: ${response.statusCode} - ${response.body}');
        return null;
      }
    } on TimeoutException {
      debugPrint('Timeout lors du géocodage de: $address');
      return null;
    } catch (e) {
      debugPrint('Exception lors du géocodage de "$address": $e');

      // Fallback pour le développement/tests
      if (address.toLowerCase().contains('paris')) {
        return LatLng(48.8566, 2.3522); // Paris
      } else if (address.toLowerCase().contains('lyon')) {
        return LatLng(45.7640, 4.8357); // Lyon
      } else if (address.toLowerCase().contains('marseille')) {
        return LatLng(43.2965, 5.3698); // Marseille
      }

      return null;
    }
  }
}

// Classe pour stocker les données d'un itinéraire
class _RouteData {
  final List<LatLng> points;
  final List<_NavigationInstruction> instructions;
  final double distance;

  _RouteData({
    required this.points,
    required this.instructions,
    required this.distance,
  });
}

// Classe pour stocker une instruction de navigation
class _NavigationInstruction {
  final String text;
  final LatLng location;
  final double distance;
  bool announced = false;

  _NavigationInstruction({
    required this.text,
    required this.location,
    required this.distance,
  });
}
