import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

/// Service pour la communication avec Arduino
/// Supporte HTTP (WiFi) et peut être étendu pour Bluetooth
class ArduinoService {
  static const String _defaultArduinoIP = '*************'; // IP de votre Arduino
  static const int _defaultPort = 80;
  static const Duration _timeout = Duration(seconds: 5);
  
  String _arduinoIP = _defaultArduinoIP;
  int _port = _defaultPort;
  bool _isConnected = false;
  
  // Getters
  bool get isConnected => _isConnected;
  String get arduinoIP => _arduinoIP;
  
  /// Configure l'adresse IP de l'Arduino
  void setArduinoIP(String ip, {int port = 80}) {
    _arduinoIP = ip;
    _port = port;
  }
  
  /// Teste la connexion avec l'Arduino
  Future<bool> testConnection() async {
    try {
      final response = await http.get(
        Uri.parse('http://$_arduinoIP:$_port/status'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(_timeout);
      
      _isConnected = response.statusCode == 200;
      return _isConnected;
    } catch (e) {
      debugPrint('Erreur de connexion Arduino: $e');
      _isConnected = false;
      return false;
    }
  }
  
  /// Envoie une commande à l'Arduino
  Future<Map<String, dynamic>?> sendCommand(String command, {Map<String, dynamic>? parameters}) async {
    if (!_isConnected && !await testConnection()) {
      throw Exception('Arduino non connecté');
    }
    
    try {
      final body = {
        'command': command,
        if (parameters != null) 'parameters': parameters,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      final response = await http.post(
        Uri.parse('http://$_arduinoIP:$_port/command'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(body),
      ).timeout(_timeout);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erreur Arduino: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Erreur envoi commande Arduino: $e');
      rethrow;
    }
  }
  
  /// Commandes spécifiques pour la domotique
  
  /// Allume/éteint une lumière
  Future<bool> controlLight(String lightId, bool turnOn) async {
    try {
      final result = await sendCommand('light', {
        'id': lightId,
        'action': turnOn ? 'on' : 'off',
      });
      return result?['success'] == true;
    } catch (e) {
      debugPrint('Erreur contrôle lumière: $e');
      return false;
    }
  }
  
  /// Contrôle un volet/store
  Future<bool> controlShutter(String shutterId, String action) async {
    // action: 'open', 'close', 'stop'
    try {
      final result = await sendCommand('shutter', {
        'id': shutterId,
        'action': action,
      });
      return result?['success'] == true;
    } catch (e) {
      debugPrint('Erreur contrôle volet: $e');
      return false;
    }
  }
  
  /// Contrôle la température/chauffage
  Future<bool> controlHeating(double temperature) async {
    try {
      final result = await sendCommand('heating', {
        'temperature': temperature,
      });
      return result?['success'] == true;
    } catch (e) {
      debugPrint('Erreur contrôle chauffage: $e');
      return false;
    }
  }
  
  /// Lit les capteurs
  Future<Map<String, dynamic>?> readSensors() async {
    try {
      final result = await sendCommand('sensors');
      return result?['data'];
    } catch (e) {
      debugPrint('Erreur lecture capteurs: $e');
      return null;
    }
  }
  
  /// Active/désactive l'alarme
  Future<bool> controlAlarm(bool activate) async {
    try {
      final result = await sendCommand('alarm', {
        'action': activate ? 'activate' : 'deactivate',
      });
      return result?['success'] == true;
    } catch (e) {
      debugPrint('Erreur contrôle alarme: $e');
      return false;
    }
  }
  
  /// Commandes vocales spécifiques
  Future<bool> processVoiceCommand(String voiceCommand) async {
    final command = voiceCommand.toLowerCase().trim();
    
    try {
      // Lumières
      if (command.contains('allume') && command.contains('lumière')) {
        return await controlLight('main', true);
      } else if (command.contains('éteins') && command.contains('lumière')) {
        return await controlLight('main', false);
      }
      
      // Volets
      else if (command.contains('ouvre') && command.contains('volet')) {
        return await controlShutter('main', 'open');
      } else if (command.contains('ferme') && command.contains('volet')) {
        return await controlShutter('main', 'close');
      }
      
      // Chauffage
      else if (command.contains('chauffage')) {
        if (command.contains('monte') || command.contains('augmente')) {
          return await controlHeating(22.0); // Température par défaut
        } else if (command.contains('baisse') || command.contains('diminue')) {
          return await controlHeating(18.0);
        }
      }
      
      // Alarme
      else if (command.contains('active') && command.contains('alarme')) {
        return await controlAlarm(true);
      } else if (command.contains('désactive') && command.contains('alarme')) {
        return await controlAlarm(false);
      }
      
      return false;
    } catch (e) {
      debugPrint('Erreur traitement commande vocale: $e');
      return false;
    }
  }
}
