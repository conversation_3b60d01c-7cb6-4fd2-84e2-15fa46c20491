import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voice_assistant/services/arduino_service.dart';
import 'package:voice_assistant/services/tts_service.dart';

class ArduinoConfigPage extends StatefulWidget {
  const ArduinoConfigPage({super.key});

  @override
  State<ArduinoConfigPage> createState() => _ArduinoConfigPageState();
}

class _ArduinoConfigPageState extends State<ArduinoConfigPage> {
  final TextEditingController _ipController = TextEditingController();
  final TextEditingController _portController = TextEditingController();
  final ArduinoService _arduinoService = ArduinoService();
  final TTSService _ttsService = TTSService();
  
  bool _isConnected = false;
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _loadSavedConfig();
    _ttsService.initialize();
  }

  Future<void> _loadSavedConfig() async {
    final prefs = await SharedPreferences.getInstance();
    final savedIP = prefs.getString('arduino_ip') ?? '*************';
    final savedPort = prefs.getInt('arduino_port') ?? 80;
    
    setState(() {
      _ipController.text = savedIP;
      _portController.text = savedPort.toString();
    });
    
    _arduinoService.setArduinoIP(savedIP, port: savedPort);
    _testConnection();
  }

  Future<void> _saveConfig() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('arduino_ip', _ipController.text);
    await prefs.setInt('arduino_port', int.tryParse(_portController.text) ?? 80);
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Test de connexion...';
    });

    try {
      _arduinoService.setArduinoIP(
        _ipController.text,
        port: int.tryParse(_portController.text) ?? 80,
      );
      
      final connected = await _arduinoService.testConnection();
      
      setState(() {
        _isConnected = connected;
        _statusMessage = connected 
          ? 'Connexion réussie ✓' 
          : 'Connexion échouée ✗';
        _isLoading = false;
      });
      
      if (connected) {
        await _saveConfig();
        _ttsService.speak("Arduino connecté avec succès");
      } else {
        _ttsService.speak("Impossible de se connecter à l'Arduino");
      }
    } catch (e) {
      setState(() {
        _isConnected = false;
        _statusMessage = 'Erreur: $e';
        _isLoading = false;
      });
      _ttsService.speak("Erreur de connexion Arduino");
    }
  }

  Future<void> _testCommand() async {
    if (!_isConnected) {
      _ttsService.speak("Arduino non connecté");
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Test de commande...';
    });

    try {
      final result = await _arduinoService.readSensors();
      setState(() {
        _statusMessage = result != null 
          ? 'Commande réussie: ${result.toString()}' 
          : 'Commande échouée';
        _isLoading = false;
      });
      
      if (result != null) {
        _ttsService.speak("Test de commande réussi");
      } else {
        _ttsService.speak("Test de commande échoué");
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur commande: $e';
        _isLoading = false;
      });
      _ttsService.speak("Erreur lors du test de commande");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration Arduino'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Connexion Arduino',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _ipController,
                      decoration: const InputDecoration(
                        labelText: 'Adresse IP Arduino',
                        hintText: '*************',
                        prefixIcon: Icon(Icons.wifi),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _portController,
                      decoration: const InputDecoration(
                        labelText: 'Port',
                        hintText: '80',
                        prefixIcon: Icon(Icons.settings_ethernet),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _testConnection,
                            icon: _isLoading 
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : Icon(_isConnected ? Icons.check_circle : Icons.wifi_find),
                            label: Text(_isLoading ? 'Test...' : 'Tester connexion'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isConnected 
                                ? Colors.green 
                                : Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: (_isConnected && !_isLoading) ? _testCommand : null,
                          icon: const Icon(Icons.play_arrow),
                          label: const Text('Test commande'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _isConnected 
                          ? Colors.green.withOpacity(0.1)
                          : Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _isConnected ? Colors.green : Colors.red,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _isConnected ? Icons.check_circle : Icons.error,
                            color: _isConnected ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _statusMessage.isEmpty 
                                ? 'Statut de connexion inconnu' 
                                : _statusMessage,
                              style: TextStyle(
                                color: _isConnected ? Colors.green : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Commandes vocales disponibles',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    _buildCommandExample('💡', 'Lumières', '"Allume la lumière" / "Éteins la lumière"'),
                    _buildCommandExample('🪟', 'Volets', '"Ouvre le volet" / "Ferme le volet"'),
                    _buildCommandExample('🌡️', 'Chauffage', '"Monte le chauffage" / "Baisse le chauffage"'),
                    _buildCommandExample('🚨', 'Alarme', '"Active l\'alarme" / "Désactive l\'alarme"'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommandExample(String icon, String title, String command) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Text(icon, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  command,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _ipController.dispose();
    _portController.dispose();
    super.dispose();
  }
}
