import 'package:flutter/material.dart';
// Import commentés pour mode simulé
// import 'package:speech_to_text/speech_to_text.dart' as stt;
// import 'package:permission_handler/permission_handler.dart';

class TestSpeechPage extends StatefulWidget {
  const TestSpeechPage({super.key});

  @override
  _TestSpeechPageState createState() => _TestSpeechPageState();
}

class _TestSpeechPageState extends State<TestSpeechPage> {
  // final stt.SpeechToText _speech = stt.SpeechToText();
  bool _isListening = false;
  String _recognizedText = '';
  bool _speechInitialized = true; // Toujours initialisé en mode simulé
  double _confidence = 0.8; // Valeur simulée
  final List<String> _transcriptionHistory = [];
  String _currentStatus = 'Prêt (mode simulé)';
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Mode simulé, pas besoin d'initialiser
    _currentStatus = 'Prêt (mode simulé)';
  }

  // Mode simulé avec délai
  void _simulateRecognition() async {
    if (_isListening) {
      // Liste de phrases à simuler
      final phrases = [
        "Bonjour, comment puis-je vous aider aujourd'hui ?",
        "Il fait beau aujourd'hui, n'est-ce pas ?",
        "Je suis votre assistant vocal en mode simulé",
        "Cette fonctionnalité est en cours de développement",
      ];

      // Choisir une phrase aléatoire
      final phrase =
          phrases[DateTime.now().millisecondsSinceEpoch % phrases.length];

      // Simuler la reconnaissance progressive
      for (int i = 1; i <= phrase.length; i++) {
        if (!_isListening) break; // Arrêter si l'écoute est désactivée

        await Future.delayed(Duration(milliseconds: 50));
        if (mounted) {
          setState(() {
            _recognizedText = phrase.substring(0, i);
          });
        }
      }

      // Simuler la fin de reconnaissance
      await Future.delayed(Duration(seconds: 2));
      if (mounted && _isListening) {
        setState(() {
          _isListening = false;
          _currentStatus = 'Prêt (mode simulé)';

          // Sauvegarder le texte reconnu
          if (_recognizedText.isNotEmpty) {
            _transcriptionHistory.add(_recognizedText);
            if (_transcriptionHistory.length > 10) {
              _transcriptionHistory.removeAt(0);
            }
          }
        });
      }
    }
  }

  void _toggleListening() async {
    if (_isListening) {
      setState(() {
        _isListening = false;
        _currentStatus = 'Arrêté';
      });
    } else {
      setState(() {
        _isListening = true;
        _recognizedText = '';
        _confidence = 0.8;
        _errorMessage = '';
        _currentStatus = 'Écoute en cours... (simulé)';
      });

      // Lancer la simulation
      _simulateRecognition();
    }
  }

  void _clearHistory() {
    setState(() {
      _transcriptionHistory.clear();
    });
  }

  @override
  void dispose() {
    // Pas besoin d'arrêter quoi que ce soit
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test de Reconnaissance Vocale (Simulé)'),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _clearHistory,
            tooltip: 'Effacer l\'historique',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Section d'état
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'État: $_currentStatus',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color:
                            _currentStatus.contains('Prêt')
                                ? Colors.green
                                : _currentStatus.contains('Écoute')
                                ? Colors.blue
                                : Colors.orange,
                      ),
                    ),
                    if (_errorMessage.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          'Erreur: $_errorMessage',
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Section de transcription
            Card(
              elevation: 3,
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Transcription (simulée):',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      constraints: const BoxConstraints(minHeight: 100),
                      child: Text(
                        _recognizedText.isEmpty
                            ? 'Appuyez sur le bouton pour simuler la parole'
                            : _recognizedText,
                        style: const TextStyle(fontSize: 18),
                      ),
                    ),
                    if (_confidence > 0)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          'Niveau de confiance: ${(_confidence * 100).toStringAsFixed(1)}% (simulé)',
                          style: TextStyle(
                            color:
                                _confidence > 0.7
                                    ? Colors.green
                                    : _confidence > 0.4
                                    ? Colors.orange
                                    : Colors.red,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Bouton d'action
            ElevatedButton.icon(
              onPressed: _toggleListening,
              icon: Icon(_isListening ? Icons.stop : Icons.mic),
              label: Text(
                _isListening
                    ? 'Arrêter la simulation'
                    : 'Commencer la simulation',
                style: const TextStyle(fontSize: 16),
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                backgroundColor: _isListening ? Colors.red : Colors.blue,
              ),
            ),

            const SizedBox(height: 30),

            // Historique
            if (_transcriptionHistory.isNotEmpty) ...[
              const Text(
                'Historique des transcriptions (simulées):',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              ...List.generate(
                _transcriptionHistory.length,
                (index) => Card(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: ListTile(
                    leading: CircleAvatar(
                      child: Text('${_transcriptionHistory.length - index}'),
                    ),
                    title: Text(
                      _transcriptionHistory[_transcriptionHistory.length -
                          1 -
                          index],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _toggleListening,
        tooltip: _isListening ? 'Arrêter' : 'Écouter',
        backgroundColor: _isListening ? Colors.red : Colors.blue,
        child: Icon(_isListening ? Icons.stop : Icons.mic),
      ),
    );
  }
}
