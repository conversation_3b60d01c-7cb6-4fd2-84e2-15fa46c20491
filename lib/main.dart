import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/config/app_config.dart';
import 'package:voice_assistant/config/firebase_config.dart';
import 'package:voice_assistant/pages/home_page.dart';
import 'package:voice_assistant/pages/login_page.dart';
import 'package:voice_assistant/pages/main_navigation_page.dart';
import 'package:voice_assistant/pages/maps_page.dart';
import 'package:voice_assistant/pages/test_speech_page.dart';
import 'package:voice_assistant/services/audio_handler.dart';
import 'package:voice_assistant/services/firebase/auth.dart';
import 'package:voice_assistant/services/awesome_notifications_service.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/voice_detection_service.dart';
import 'package:firebase_auth/firebase_auth.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialiser Firebase
  await FirebaseConfig.initialize();

  // Initialiser la configuration de l'application
  await AppConfig.initialize();

  // Initialiser l'audio
  await AudioConfig.initialize();

  // Initialiser les services
  await AwesomeNotificationsService().initialize();
  await TtsService().initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<VoiceDetectionService>(
          create: (_) => VoiceDetectionService(),
        ),
        ChangeNotifierProvider<ThemeService>(create: (_) => ThemeService()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);

    return MaterialApp(
      title: 'Assistant Vocal',
      debugShowCheckedModeBanner: false,
      theme: themeService.getThemeData(context),
      themeMode: themeService.themeMode,
      initialRoute: '/',
      routes: {
        '/': (context) => const AuthCheckScreen(),
        '/main': (context) => const MainNavigationPage(),
        '/login': (context) => const LoginPage(),
        '/test_speech': (context) => const TestSpeechPage(),
        '/maps': (context) => const MapsPage(),
      },
    );
  }
}

class AuthCheckScreen extends StatelessWidget {
  const AuthCheckScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: AuthService().authStateChanges(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasData) {
          return const MainNavigationPage();
        }

        return const LoginPage();
      },
    );
  }
}
