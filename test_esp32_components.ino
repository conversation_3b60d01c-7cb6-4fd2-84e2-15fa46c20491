/*
 * Script de test pour tous les composants ESP32
 * À utiliser pour vérifier que tout fonctionne avant l'intégration complète
 */

#include <DHT.h>
#include <ESP32Servo.h>

// === CONFIGURATION DES PINS ===
const int LED_PIN = 2;
const int DHT_PIN = 4;
const int SERVO_PIN = 18;
const int PIR_PIN = 19;
const int HALL_PIN = 21;

#define DHT_TYPE DHT22

// === INITIALISATION DES COMPOSANTS ===
DHT dht(DHT_PIN, DHT_TYPE);
Servo testServo;

void setup() {
  Serial.begin(115200);
  Serial.println("\n🧪 === TEST DES COMPOSANTS ESP32 ===");
  Serial.println("Vérification de tous les composants...\n");
  
  // Configuration des pins
  pinMode(LED_PIN, OUTPUT);
  pinMode(PIR_PIN, INPUT);
  pinMode(HALL_PIN, INPUT_PULLUP);
  
  // Initialisation des composants
  dht.begin();
  testServo.attach(SERVO_PIN);
  
  Serial.println("✓ Configuration terminée");
  Serial.println("Début des tests dans 3 secondes...\n");
  delay(3000);
}

void loop() {
  Serial.println("🔄 === CYCLE DE TEST ===");
  
  // Test 1: LED
  testLED();
  delay(2000);
  
  // Test 2: DHT22
  testDHT22();
  delay(2000);
  
  // Test 3: Servomoteur
  testServo();
  delay(2000);
  
  // Test 4: Capteur PIR
  testPIR();
  delay(1000);
  
  // Test 5: Capteur Hall
  testHall();
  delay(1000);
  
  Serial.println("📊 === RÉSUMÉ DU CYCLE ===");
  printSystemInfo();
  
  Serial.println("\n⏳ Attente 10 secondes avant le prochain cycle...\n");
  delay(10000);
}

// === FONCTIONS DE TEST ===

void testLED() {
  Serial.println("💡 Test LED:");
  
  // Allumer
  digitalWrite(LED_PIN, HIGH);
  Serial.println("  → LED allumée");
  delay(1000);
  
  // Éteindre
  digitalWrite(LED_PIN, LOW);
  Serial.println("  → LED éteinte");
  delay(500);
  
  // Clignotement rapide
  Serial.println("  → Clignotement rapide (3x)");
  for (int i = 0; i < 3; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(200);
    digitalWrite(LED_PIN, LOW);
    delay(200);
  }
  
  Serial.println("  ✓ Test LED terminé\n");
}

void testDHT22() {
  Serial.println("🌡️ Test DHT22:");
  
  float temperature = dht.readTemperature();
  float humidity = dht.readHumidity();
  
  if (isnan(temperature) || isnan(humidity)) {
    Serial.println("  ❌ Erreur lecture DHT22");
    Serial.println("  → Vérifiez les connexions");
    Serial.println("  → VCC: 3.3V, GND: GND, DATA: GPIO4");
  } else {
    Serial.println("  ✓ DHT22 fonctionne correctement");
    Serial.printf("  → Température: %.1f°C\n", temperature);
    Serial.printf("  → Humidité: %.1f%%\n", humidity);
    
    // Vérification des valeurs
    if (temperature < -40 || temperature > 80) {
      Serial.println("  ⚠️ Température hors limites normales");
    }
    if (humidity < 0 || humidity > 100) {
      Serial.println("  ⚠️ Humidité hors limites normales");
    }
  }
  
  Serial.println("  ✓ Test DHT22 terminé\n");
}

void testServo() {
  Serial.println("🔧 Test Servomoteur:");
  
  // Position 0°
  testServo.write(0);
  Serial.println("  → Position 0° (fermé)");
  delay(1000);
  
  // Position 90°
  testServo.write(90);
  Serial.println("  → Position 90° (mi-ouvert)");
  delay(1000);
  
  // Position 180°
  testServo.write(180);
  Serial.println("  → Position 180° (ouvert)");
  delay(1000);
  
  // Retour à 0°
  testServo.write(0);
  Serial.println("  → Retour position 0°");
  delay(500);
  
  // Test de balayage
  Serial.println("  → Balayage lent (0° → 180° → 0°)");
  for (int pos = 0; pos <= 180; pos += 10) {
    testServo.write(pos);
    delay(100);
  }
  for (int pos = 180; pos >= 0; pos -= 10) {
    testServo.write(pos);
    delay(100);
  }
  
  Serial.println("  ✓ Test Servomoteur terminé\n");
}

void testPIR() {
  Serial.println("👤 Test Capteur PIR:");
  
  bool motionState = digitalRead(PIR_PIN);
  
  if (motionState) {
    Serial.println("  🟢 MOUVEMENT DÉTECTÉ!");
    Serial.println("  → Le capteur PIR fonctionne");
    
    // Clignoter LED pour indiquer détection
    for (int i = 0; i < 2; i++) {
      digitalWrite(LED_PIN, HIGH);
      delay(150);
      digitalWrite(LED_PIN, LOW);
      delay(150);
    }
  } else {
    Serial.println("  🔵 Aucun mouvement");
    Serial.println("  → Bougez devant le capteur pour tester");
  }
  
  Serial.println("  ℹ️ Note: Le PIR a besoin de 1 minute pour se stabiliser");
  Serial.println("  ✓ Test PIR terminé\n");
}

void testHall() {
  Serial.println("🧲 Test Capteur Hall:");
  
  bool hallState = digitalRead(HALL_PIN);
  
  // État inversé car pull-up
  if (!hallState) {
    Serial.println("  🟢 CHAMP MAGNÉTIQUE DÉTECTÉ!");
    Serial.println("  → Aimant proche du capteur");
    
    // Clignoter LED pour indiquer détection
    digitalWrite(LED_PIN, HIGH);
    delay(300);
    digitalWrite(LED_PIN, LOW);
  } else {
    Serial.println("  🔵 Aucun champ magnétique");
    Serial.println("  → Approchez un aimant du capteur");
  }
  
  Serial.println("  ℹ️ Note: Utilisez un aimant néodyme fort");
  Serial.println("  ✓ Test Hall terminé\n");
}

void printSystemInfo() {
  Serial.println("📋 Informations système:");
  Serial.printf("  → Temps de fonctionnement: %lu ms\n", millis());
  Serial.printf("  → Mémoire libre: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("  → Fréquence CPU: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("  → Version SDK: %s\n", ESP.getSdkVersion());
  
  // État des pins
  Serial.println("📌 État des pins:");
  Serial.printf("  → LED (GPIO%d): %s\n", LED_PIN, digitalRead(LED_PIN) ? "HIGH" : "LOW");
  Serial.printf("  → PIR (GPIO%d): %s\n", PIR_PIN, digitalRead(PIR_PIN) ? "HIGH" : "LOW");
  Serial.printf("  → Hall (GPIO%d): %s\n", HALL_PIN, digitalRead(HALL_PIN) ? "HIGH" : "LOW");
  
  Serial.println();
}

/*
 * GUIDE D'UTILISATION:
 * 
 * 1. Téléversez ce code sur votre ESP32
 * 2. Ouvrez le moniteur série (115200 baud)
 * 3. Observez les tests automatiques
 * 4. Interagissez avec les capteurs:
 *    - Bougez devant le PIR
 *    - Approchez un aimant du capteur Hall
 * 
 * RÉSULTATS ATTENDUS:
 * ✓ LED clignote et s'allume/éteint
 * ✓ DHT22 affiche température et humidité
 * ✓ Servomoteur bouge de 0° à 180°
 * ✓ PIR détecte les mouvements
 * ✓ Hall détecte les aimants
 * 
 * EN CAS DE PROBLÈME:
 * ❌ DHT22 → Vérifiez connexions et alimentation 3.3V
 * ❌ Servo → Vérifiez alimentation 5V et signal GPIO18
 * ❌ PIR → Attendez 1 minute, vérifiez alimentation 5V
 * ❌ Hall → Utilisez aimant plus fort, vérifiez GPIO21
 */
