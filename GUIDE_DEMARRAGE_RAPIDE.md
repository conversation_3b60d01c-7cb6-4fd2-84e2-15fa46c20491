# 🚀 Guide de démarrage rapide - ESP32 Domotique Vocale

## 📦 Votre matériel
- ✅ ESP32 DevKit V1
- ✅ DHT22 (température/humidité)
- ✅ Servomoteur SG90
- ✅ Capteur magnétique Hall A3144
- ✅ LED + résistance 220Ω
- ✅ Capteur PIR HC-SR501 (mouvement)
- ✅ Breadboard + fils

## ⚡ Étapes de configuration

### 1. 🔌 Câblage (5 minutes)
```
ESP32 Pin    →    Composant
================================
GPIO2        →    LED + résistance 220Ω → GND
GPIO4        →    DHT22 Data
GPIO18       →    Servomoteur Signal (orange)
GPIO19       →    PIR OUT
GPIO21       →    Hall OUT
3.3V         →    DHT22 VCC, Hall VCC
5V           →    Servo VCC (rouge), PIR VCC
GND          →    Tous les GND (noir/marron)
```

### 2. 💻 Installation logicielle (10 minutes)

#### A. Arduino IDE
```bash
sudo snap install arduino
sudo usermod -a -G dialout $USER
# Redémarrer la session
```

#### B. Bibliothèques Arduino
Dans l'IDE Arduino → Outils → Gérer les bibliothèques :
- **ArduinoJson** by Benoit Blanchon
- **DHT sensor library** by Adafruit
- **ESP32Servo** by Kevin Harrington

#### C. Configuration ESP32
1. Fichier → Préférences → URLs supplémentaires :
   ```
   https://dl.espressif.com/dl/package_esp32_index.json
   ```
2. Outils → Type de carte → Gestionnaire → "esp32" → Installer

### 3. 📝 Code Arduino (2 minutes)

#### A. Modifier le WiFi
Dans `arduino_domotique.ino` :
```cpp
const char* ssid = "VOTRE_WIFI_SSID";      // ← Votre nom WiFi
const char* password = "VOTRE_WIFI_PASSWORD"; // ← Votre mot de passe
```

#### B. Téléverser
1. Connecter ESP32 via USB
2. Outils → Type de carte → "ESP32 Dev Module"
3. Outils → Port → Sélectionner le port (ttyUSB0 ou ttyACM0)
4. Cliquer **Téléverser** ⬆️

#### C. Noter l'IP
Ouvrir Moniteur série (Ctrl+Shift+M) :
```
🌐 Connexion WiFi....
✓ WiFi connecté! IP: *************  ← Noter cette IP
✓ Serveur web démarré sur le port 80
🎙️ Prêt pour les commandes vocales!
```

### 4. 📱 Configuration Flutter (3 minutes)

#### A. Lancer l'application
```bash
cd /home/<USER>/voiceassistant
flutter run
```

#### B. Configurer Arduino
1. Dans l'app → Cliquer l'icône **⚡** (en haut à droite)
2. Entrer l'IP notée (ex: *************)
3. Cliquer **"Tester connexion"**
4. Vérifier : ✅ **"Connexion réussie"**

### 5. 🎙️ Test des commandes vocales (2 minutes)

#### Commandes disponibles :
- 💡 **"Allume la lumière"** / **"Éteins la lumière"**
- 🪟 **"Ouvre le volet"** / **"Ferme le volet"**
- 🚨 **"Active l'alarme"** / **"Désactive l'alarme"**
- 📊 **"Quelle est la température ?"**

#### Test :
1. Appuyer sur le bouton micro 🎤 dans l'app
2. Dire : **"Allume la lumière"**
3. Vérifier : LED s'allume ✨
4. Dire : **"Ouvre le volet"**
5. Vérifier : Servomoteur bouge à 90° 🔧

## 🧪 Tests des capteurs

### DHT22 (Température/Humidité)
- **Test** : Souffler sur le capteur
- **Résultat** : Température et humidité changent dans l'app

### PIR (Mouvement)
- **Test** : Bouger devant le capteur
- **Résultat** : Console série affiche "👤 Mouvement détecté"

### Hall (Magnétique)
- **Test** : Approcher un aimant
- **Résultat** : Console série affiche "🚪 Porte ouverte"

### Servomoteur
- **Test** : Commande vocale "Ouvre le volet"
- **Résultat** : Servo tourne de 0° à 90°

## 🔧 Dépannage rapide

### ❌ ESP32 ne se connecte pas au WiFi
```
Solution : Vérifier SSID/mot de passe, utiliser WiFi 2.4GHz
```

### ❌ App ne trouve pas l'Arduino
```
Solution : Vérifier IP, même réseau WiFi, redémarrer ESP32
```

### ❌ DHT22 affiche NaN
```
Solution : Vérifier connexions, attendre 2 secondes
```

### ❌ Servomoteur ne bouge pas
```
Solution : Vérifier alimentation 5V, connexion GPIO18
```

### ❌ Commandes vocales ne marchent pas
```
Solution : Vérifier connexion Arduino, permissions micro
```

## 🎯 Utilisation quotidienne

### Démarrage :
1. **Allumer ESP32** (brancher USB)
2. **Attendre 30 secondes** (connexion WiFi)
3. **Lancer l'app Flutter**
4. **Commencer à parler** ! 🎙️

### Commandes utiles :
- **Contrôle LED** : "Allume/Éteins la lumière"
- **Contrôle servo** : "Ouvre/Ferme le volet"
- **Sécurité** : "Active/Désactive l'alarme"
- **Info capteurs** : "Quelle température ?"

### Surveillance :
- **Console série** : Voir les actions en temps réel
- **App Flutter** : Interface graphique
- **LED ESP32** : Clignote lors des commandes

## 🚀 Évolutions possibles

### Court terme :
- **Programmation horaire** : Automatiser selon l'heure
- **Seuils capteurs** : Alertes température/humidité
- **Plus de servos** : Contrôler plusieurs volets

### Long terme :
- **Interface web** : Contrôle depuis navigateur
- **Base de données** : Historique des actions
- **Notifications push** : Alertes sur téléphone
- **Intégration domotique** : Home Assistant, etc.

## 📞 Support

### En cas de problème :
1. **Vérifier les connexions** physiques
2. **Consulter le moniteur série** Arduino
3. **Redémarrer ESP32** (débrancher/rebrancher)
4. **Vérifier le réseau WiFi**
5. **Tester composant par composant**

### Logs utiles :
- **Arduino** : Moniteur série (115200 baud)
- **Flutter** : Console de développement
- **Réseau** : `ping IP_ARDUINO`

---

## ✅ Checklist finale

- [ ] Câblage vérifié selon le schéma
- [ ] Code Arduino téléversé avec bon WiFi
- [ ] IP Arduino notée et testée
- [ ] App Flutter configurée avec l'IP
- [ ] Test "Allume la lumière" réussi
- [ ] Test "Ouvre le volet" réussi
- [ ] Capteurs DHT22 fonctionnels
- [ ] Détection mouvement PIR active
- [ ] Capteur Hall réactif aux aimants

**🎉 Félicitations ! Votre système de domotique vocale est opérationnel !**

Temps total d'installation : **~20 minutes**
Prêt pour les commandes vocales ! 🎙️🏠
