# Guide d'installation Arduino pour la domotique vocale

## Prérequis Ubuntu

### 1. Installation de l'IDE Arduino
```bash
# Méthode recommandée via Snap
sudo snap install arduino

# Ou téléchargement direct
wget https://downloads.arduino.cc/arduino-ide/arduino-ide_2.3.2_Linux_64bit.AppImage
chmod +x arduino-ide_2.3.2_Linux_64bit.AppImage
sudo mv arduino-ide_2.3.2_Linux_64bit.AppImage /usr/local/bin/arduino-ide
```

### 2. Configuration des permissions
```bash
# Ajouter votre utilisateur au groupe dialout
sudo usermod -a -G dialout $USER

# Redémarrer la session
newgrp dialout
# Ou redémarrer l'ordinateur
```

### 3. Vérification de la détection USB
```bash
# Vérifier que votre Arduino est détecté
lsusb
dmesg | grep tty

# Vous devriez voir quelque chose comme:
# ttyUSB0 ou ttyACM0
```

## Matériel recommandé

### Option 1: ESP32 (Recommandée - WiFi intégré)
- **Carte**: ESP32 DevKit V1
- **Avantages**: WiFi intégré, plus de mémoire, plus rapide
- **Prix**: ~10-15€

### Option 2: Arduino Uno + Module WiFi
- **Carte**: Arduino Uno R3
- **Module WiFi**: ESP8266 (ESP-01) ou Shield WiFi
- **Avantages**: Plus simple pour débuter
- **Prix**: ~20-25€

### Composants pour la domotique
- **Relais 4 canaux** (pour contrôler lumières, volets, etc.)
- **Capteur de température** (DS18B20 ou DHT22)
- **Capteur de mouvement** (PIR HC-SR501)
- **Breadboard et câbles de connexion**
- **Alimentation 5V/12V** selon vos besoins

## Installation des bibliothèques Arduino

### Pour ESP32:
1. Ouvrir l'IDE Arduino
2. Aller dans **Fichier > Préférences**
3. Ajouter cette URL dans "URLs de gestionnaire de cartes supplémentaires":
   ```
   https://dl.espressif.com/dl/package_esp32_index.json
   ```
4. Aller dans **Outils > Type de carte > Gestionnaire de cartes**
5. Rechercher "ESP32" et installer "esp32 by Espressif Systems"

### Bibliothèques nécessaires:
1. **ArduinoJson** (pour la communication JSON)
   - Aller dans **Outils > Gérer les bibliothèques**
   - Rechercher "ArduinoJson" et installer

2. **WiFi** (incluse avec ESP32)

## Configuration du code Arduino

### 1. Modifier les paramètres WiFi
Dans le fichier `arduino_domotique.ino`:
```cpp
const char* ssid = "VOTRE_WIFI_SSID";        // Remplacer par votre nom WiFi
const char* password = "VOTRE_WIFI_PASSWORD"; // Remplacer par votre mot de passe WiFi
```

### 2. Adapter les pins selon votre montage
```cpp
// Pins pour les relais/sorties
const int LIGHT_PIN = 2;          // Pin pour contrôler la lumière
const int SHUTTER_OPEN_PIN = 4;   // Pin pour ouvrir le volet
const int SHUTTER_CLOSE_PIN = 5;  // Pin pour fermer le volet
const int HEATING_PIN = 18;       // Pin pour le chauffage
const int ALARM_PIN = 19;         // Pin pour l'alarme

// Pins pour les capteurs
const int TEMP_SENSOR_PIN = A0;   // Pin analogique pour capteur température
const int MOTION_SENSOR_PIN = 21; // Pin pour capteur de mouvement
```

### 3. Téléverser le code
1. Connecter votre ESP32/Arduino via USB
2. Sélectionner la bonne carte dans **Outils > Type de carte**
3. Sélectionner le bon port dans **Outils > Port**
4. Cliquer sur **Téléverser**

## Configuration de l'application Flutter

### 1. Trouver l'IP de votre Arduino
Après le téléversement, ouvrir le **Moniteur série** (Ctrl+Shift+M):
```
Connexion WiFi....
Connecté! IP: *************
Serveur démarré
```
Noter l'adresse IP affichée.

### 2. Configurer l'IP dans l'application
1. Lancer votre application Flutter
2. Aller dans les paramètres Arduino
3. Entrer l'IP de votre Arduino (ex: *************)
4. Tester la connexion

## Schéma de câblage de base

### Connexions pour ESP32:
```
ESP32          Composant
GPIO2    ----> LED/Relais Lumière
GPIO4    ----> Relais Volet Ouverture
GPIO5    ----> Relais Volet Fermeture
GPIO18   ----> Relais Chauffage
GPIO19   ----> Buzzer/LED Alarme
GPIO21   ----> Capteur PIR (mouvement)
A0       ----> Capteur température
GND      ----> GND commun
3.3V     ----> VCC capteurs
```

### Sécurité importante:
- **JAMAIS** connecter directement des charges 220V à l'Arduino
- Utiliser des relais appropriés pour les charges importantes
- Prévoir des fusibles et disjoncteurs
- Faire vérifier par un électricien pour les installations permanentes

## Test des commandes vocales

### Commandes disponibles:
- **Lumières**: "Allume la lumière" / "Éteins la lumière"
- **Volets**: "Ouvre le volet" / "Ferme le volet"
- **Chauffage**: "Monte le chauffage" / "Baisse le chauffage"
- **Alarme**: "Active l'alarme" / "Désactive l'alarme"

### Dépannage:

#### Arduino non détecté:
```bash
# Vérifier les permissions
ls -l /dev/ttyUSB* /dev/ttyACM*
# Si pas d'accès, ajouter aux groupes:
sudo usermod -a -G dialout,tty $USER
```

#### Problème de connexion WiFi:
1. Vérifier SSID et mot de passe
2. Vérifier que le WiFi est en 2.4GHz (ESP32 ne supporte pas 5GHz)
3. Vérifier la portée WiFi

#### Application ne se connecte pas:
1. Vérifier que Arduino et téléphone sont sur le même réseau
2. Vérifier l'IP dans le moniteur série
3. Tester avec `ping IP_ARDUINO` depuis Ubuntu
4. Vérifier le pare-feu

## Évolutions possibles

### Fonctionnalités avancées:
- **Capteurs supplémentaires**: Humidité, luminosité, qualité air
- **Contrôle par zones**: Différentes pièces
- **Programmation horaire**: Automatisation selon l'heure
- **Interface web**: Contrôle depuis un navigateur
- **Base de données**: Historique des actions
- **Notifications push**: Alertes sur le téléphone

### Sécurité renforcée:
- **Authentification**: Mot de passe pour les commandes
- **Chiffrement**: HTTPS au lieu de HTTP
- **VPN**: Accès sécurisé à distance
- **Logs**: Traçabilité des actions

## Support

En cas de problème:
1. Vérifier les connexions physiques
2. Consulter le moniteur série Arduino
3. Tester la connexion réseau
4. Vérifier les logs de l'application Flutter

Bon projet de domotique ! 🏠🎙️
